package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
	"os"

	"gorm.io/gorm"
)

type DomainRepository struct {
	db *gorm.DB
}

func NewDomainRepository(db *gorm.DB) ports.DomainRepository {
	return &DomainRepository{db: db}
}

func (r *DomainRepository) Insert(domain *domain.Domain) error {
	err := r.db.Create(domain).Error
	if err != nil {
		return err
	}

	// Reload the dns with its relationships
	return r.db.Preload("Namespace").First(domain, domain.ID).Error
}

func (r *DomainRepository) FindByID(id uint64) (*domain.Domain, error) {
	var domain domain.Domain
	err := r.db.Preload("Namespace").
		Preload("Namespace.Cluster").
		First(&domain, id).Error
	if err != nil {
		return nil, err
	}
	return &domain, nil
}

func (r *DomainRepository) FindAll(filter *ports.DomainFilter) ([]*domain.Domain, error) {
	var domains []*domain.Domain
	query := r.db.Preload("Namespace").
		Preload("Namespace.Cluster").
		Order("index DESC")

	// Always exclude CLOUDFLARE_MASTER_ZONE_NAME from results
	if masterZoneName := os.Getenv("CLOUDFLARE_MASTER_ZONE_NAME"); masterZoneName != "" {
		query = query.Where("name != ?", masterZoneName)
	}

	if filter != nil {
		if filter.Name != nil {
			query = query.Where("name LIKE ?", "%"+*filter.Name+"%")
		}
		if filter.IsDefault != nil {
			query = query.Where("is_default = ?", *filter.IsDefault)
		}
		if filter.IsActive != nil {
			query = query.Where("is_active = ?", *filter.IsActive)
		}
		if filter.NamespaceID != nil {
			query = query.Where("namespace_id = ?", *filter.NamespaceID)
		}
	}

	err := query.Find(&domains).Error
	if err != nil {
		return nil, err
	}
	return domains, nil
}

func (r *DomainRepository) Update(domain *domain.Domain) error {
	return r.db.Save(domain).Error
}

func (r *DomainRepository) Delete(id uint64) error {
	return r.db.Delete(&domain.Domain{}, id).Error
}

func (r *DomainRepository) DeleteByNamespaceID(namespaceID uint64) error {
	return r.db.Where("namespace_id = ?", namespaceID).Delete(&domain.Domain{}).Error
}

func (r *DomainRepository) FindAllWithPagination(filter *ports.DomainFilter) ([]*domain.Domain, uint64, error) {
	var domains []*domain.Domain
	var total int64

	// Build base query with preloads
	query := r.db.Preload("Namespace").
		Preload("Namespace.Cluster")

	// Always exclude CLOUDFLARE_MASTER_ZONE_NAME from results
	if masterZoneName := os.Getenv("CLOUDFLARE_MASTER_ZONE_NAME"); masterZoneName != "" {
		query = query.Where("name != ?", masterZoneName)
	}

	// Apply basic domain filters
	if filter != nil {
		if filter.Name != nil {
			query = query.Where("name LIKE ?", "%"+*filter.Name+"%")
		}
		if filter.IsDefault != nil {
			query = query.Where("is_default = ?", *filter.IsDefault)
		}
		if filter.IsActive != nil {
			query = query.Where("is_active = ?", *filter.IsActive)
		}
		if filter.NamespaceID != nil {
			query = query.Where("namespace_id = ?", *filter.NamespaceID)
		}
		// Apply order-related filters using subquery (simpler approach)
		if filter.OrderLineCode != nil || filter.OrderName != nil {
			// Build subquery to find namespace_ids that match order criteria
			subquery := r.db.Table("order_namespace").
				Select("DISTINCT order_namespace.namespace_id").
				Joins("JOIN \"order\" ON order_namespace.order_id = \"order\".id")

			if filter.OrderLineCode != nil {
				subquery = subquery.Where("\"order\".line_code LIKE ?", "%"+*filter.OrderLineCode+"%")
			}
			if filter.OrderName != nil {
				subquery = subquery.Where("\"order\".name LIKE ?", "%"+*filter.OrderName+"%")
			}

			// Use the subquery to filter domains
			query = query.Where("namespace_id IN (?)", subquery).Order("index DESC")
		} else {
			// Add ORDER BY when not using order filters
			query = query.Order("index DESC")
		}
	}

	// Get total count before applying pagination
	countQuery := query.Session(&gorm.Session{})
	if err := countQuery.Model(&domain.Domain{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	if filter != nil && filter.Page != nil && filter.PageSize != nil {
		offset := (*filter.Page - 1) * *filter.PageSize
		query = query.Offset(int(offset)).Limit(int(*filter.PageSize))
	}

	err := query.Find(&domains).Error
	if err != nil {
		return nil, 0, err
	}

	// Always populate order data for all domains (not just when filters are used)
	err = r.populateOrderData(domains)
	if err != nil {
		return nil, 0, err
	}

	return domains, uint64(total), nil
}

// populateOrderData fills in order information for domains
func (r *DomainRepository) populateOrderData(domains []*domain.Domain) error {
	if len(domains) == 0 {
		return nil
	}

	// Get namespace IDs from domains
	var namespaceIDs []uint64
	for _, d := range domains {
		namespaceIDs = append(namespaceIDs, d.NamespaceID)
	}

	// Query order data for these namespaces
	type OrderData struct {
		NamespaceID uint64  `json:"namespace_id"`
		LineCode    *string `json:"line_code"`
		Name        *string `json:"name"`
	}

	var orderData []OrderData
	err := r.db.Table("order_namespace").
		Select("order_namespace.namespace_id, \"order\".line_code, \"order\".name").
		Joins("JOIN \"order\" ON order_namespace.order_id = \"order\".id").
		Where("order_namespace.namespace_id IN ?", namespaceIDs).
		Scan(&orderData).Error
	if err != nil {
		return err
	}

	// Create a map for quick lookup
	orderMap := make(map[uint64]OrderData)
	for _, od := range orderData {
		orderMap[od.NamespaceID] = od
	}

	// Populate order data in domains
	for _, d := range domains {
		if od, exists := orderMap[d.NamespaceID]; exists {
			d.OrderLineCode = od.LineCode
			d.OrderName = od.Name
		}
	}

	return nil
}
