package services

import (
	"errors"
	"testing"

	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
)

// Mock repositories for testing
type mockOrderNamespaceRepository struct {
	orderNamespaces []*domain.OrderNamespace
	findAllError    error
}

func (m *mockOrderNamespaceRepository) Insert(orderNamespace *domain.OrderNamespace) error {
	return nil
}

func (m *mockOrderNamespaceRepository) FindAll(filter *ports.OrderNamespaceFilter) ([]*domain.OrderNamespace, error) {
	if m.findAllError != nil {
		return nil, m.findAllError
	}
	
	if filter != nil && filter.NamespaceID != nil {
		var filtered []*domain.OrderNamespace
		for _, on := range m.orderNamespaces {
			if on.NamespaceID == *filter.NamespaceID {
				filtered = append(filtered, on)
			}
		}
		return filtered, nil
	}
	
	return m.orderNamespaces, nil
}

func (m *mockOrderNamespaceRepository) FindByID(id uint64) (*domain.OrderNamespace, error) {
	return nil, nil
}

func (m *mockOrderNamespaceRepository) Update(orderNamespace *domain.OrderNamespace) error {
	return nil
}

func (m *mockOrderNamespaceRepository) Delete(id uint64) error {
	return nil
}

type mockNamespaceRepository struct {
	namespace *domain.Namespace
	findError error
}

func (m *mockNamespaceRepository) Insert(namespace *domain.Namespace) error {
	return nil
}

func (m *mockNamespaceRepository) FindAll(filter *ports.NamespaceFilter) ([]*domain.Namespace, error) {
	return nil, nil
}

func (m *mockNamespaceRepository) FindByID(id uint64) (*domain.Namespace, error) {
	if m.findError != nil {
		return nil, m.findError
	}
	return m.namespace, nil
}

func (m *mockNamespaceRepository) Update(namespace *domain.Namespace) error {
	return nil
}

func (m *mockNamespaceRepository) Delete(id uint64) error {
	return nil
}

func (m *mockNamespaceRepository) CountByType(namespaceType domain.NamespaceType) (int64, error) {
	return 0, nil
}

type mockClusterRepository struct {
	cluster   *domain.Cluster
	findError error
}

func (m *mockClusterRepository) Insert(cluster *domain.Cluster) error {
	return nil
}

func (m *mockClusterRepository) FindAll(filter *ports.ClusterFilter) ([]*domain.Cluster, error) {
	return nil, nil
}

func (m *mockClusterRepository) FindByID(id, namespaceId uint64) (*domain.Cluster, error) {
	if m.findError != nil {
		return nil, m.findError
	}
	return m.cluster, nil
}

func (m *mockClusterRepository) FindByName(name string) (*domain.Cluster, error) {
	return nil, nil
}

func (m *mockClusterRepository) Update(cluster *domain.Cluster) error {
	return nil
}

func (m *mockClusterRepository) Delete(id uint64) error {
	return nil
}

type mockWorkspaceRepository struct {
	workspace *domain.Workspace
	findError error
}

func (m *mockWorkspaceRepository) Insert(workspace *domain.Workspace) error {
	return nil
}

func (m *mockWorkspaceRepository) FindByID(id uint64) (*domain.Workspace, error) {
	if m.findError != nil {
		return nil, m.findError
	}
	return m.workspace, nil
}

func (m *mockWorkspaceRepository) FindByUserID(userID uint64) ([]*domain.Workspace, error) {
	return nil, nil
}

func (m *mockWorkspaceRepository) FindAll(filter *ports.WorkspaceFilter) ([]*domain.Workspace, error) {
	return nil, nil
}

func (m *mockWorkspaceRepository) Update(workspace *domain.Workspace) error {
	return nil
}

func (m *mockWorkspaceRepository) Delete(id uint64) error {
	return nil
}

func TestDomainService_GetUserIDFromNamespaceID(t *testing.T) {
	tests := []struct {
		name                    string
		namespaceID             uint64
		orderNamespaces         []*domain.OrderNamespace
		orderNamespaceError     error
		namespace               *domain.Namespace
		namespaceError          error
		cluster                 *domain.Cluster
		clusterError            error
		workspace               *domain.Workspace
		workspaceError          error
		expectedUserID          uint64
		expectedError           string
	}{
		{
			name:        "Success via OrderNamespace",
			namespaceID: 1,
			orderNamespaces: []*domain.OrderNamespace{
				{
					NamespaceID: 1,
					Order: &domain.Order{
						User: &domain.User{
							BaseModel: domain.BaseModel{ID: 123},
						},
					},
				},
			},
			expectedUserID: 123,
		},
		{
			name:               "Success via Workspace",
			namespaceID:        1,
			orderNamespaces:    []*domain.OrderNamespace{}, // Empty, so it will try workspace approach
			orderNamespaceError: nil,
			namespace: &domain.Namespace{
				ClusterID: 1,
			},
			cluster: &domain.Cluster{
				WorkspaceID: 1,
			},
			workspace: &domain.Workspace{
				User: &domain.User{
					BaseModel: domain.BaseModel{ID: 456},
				},
			},
			expectedUserID: 456,
		},
		{
			name:          "Invalid namespace ID",
			namespaceID:   0,
			expectedError: "namespace ID is required",
		},
		{
			name:               "Namespace not found",
			namespaceID:        1,
			orderNamespaces:    []*domain.OrderNamespace{},
			namespaceError:     errors.New("not found"),
			expectedError:      "namespace not found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock repositories
			orderNamespaceRepo := &mockOrderNamespaceRepository{
				orderNamespaces: tt.orderNamespaces,
				findAllError:    tt.orderNamespaceError,
			}
			namespaceRepo := &mockNamespaceRepository{
				namespace: tt.namespace,
				findError: tt.namespaceError,
			}
			clusterRepo := &mockClusterRepository{
				cluster:   tt.cluster,
				findError: tt.clusterError,
			}
			workspaceRepo := &mockWorkspaceRepository{
				workspace: tt.workspace,
				findError: tt.workspaceError,
			}

			// Create domain service with mocks
			domainService := &DomainService{
				orderNamespaceRepo: orderNamespaceRepo,
				namespaceRepo:      namespaceRepo,
				clusterRepo:        clusterRepo,
				workspaceRepo:      workspaceRepo,
			}

			// Test the method
			userID, err := domainService.GetUserIDFromNamespaceID(tt.namespaceID)

			// Check results
			if tt.expectedError != "" {
				if err == nil {
					t.Errorf("Expected error %q, but got nil", tt.expectedError)
				} else if err.Error() != tt.expectedError {
					t.Errorf("Expected error %q, but got %q", tt.expectedError, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error, but got %q", err.Error())
				}
				if userID != tt.expectedUserID {
					t.Errorf("Expected user ID %d, but got %d", tt.expectedUserID, userID)
				}
			}
		})
	}
}
